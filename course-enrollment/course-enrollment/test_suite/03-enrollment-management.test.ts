import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createCourseOpen,
  createCourseDraft,
  createCourseFullCapacity,
  createCourseWithWaitlist,
  createStudentEnrolledCourse,
  createStudentMaxCreditLoad,
  createTermEnrollmentOpen,
  createTermConcluded,
  createCourseCompleted,
  createStudentWithDroppedCourses,
  getStudentEnrollment,
  validateErrorEnvelope,
  handleApiResponse,
  getSuccessData,
  assertEnrollment,
  UserRole,
  EnrollmentState,
  ApiErrorId, DROP_PENALTY_FEE,
  DeliveryMode,
  makeRequest
} from './helpers';
import { v4 as uuidv4 } from 'uuid';

describe('TC-53: Enroll in Course - Success (Seat Available)', () => {
  it('should successfully enroll student when seat is available', async () => {
    const { term, course } = await createCourseOpen('CS101', 5);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

    const enrollment = handleApiResponse(response, 201);
    assertEnrollment(enrollment);
    expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_A.id);
    expect(enrollment.course_id).toBe(course.id);
    expect(enrollment.state).toBe(EnrollmentState.ENROLLED);

    const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const courseResponse = await apiClient.getCourse(term.id, course.id, regHeaders);
    const updatedCourse = getSuccessData(courseResponse);
    expect(updatedCourse.available_seats).toBe(course.capacity - 1);
    expect(updatedCourse.enrolled_count).toBe(1);

  });
});

describe('TC-54: Enroll in Course - Success (Goes to Waitlist)', () => {
  it('should place student on waitlist when course is full', async () => {
    const { term, course } = await createCourseFullCapacity();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

    const enrollment = handleApiResponse(response, 201);
    assertEnrollment(enrollment);
    expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_C.id);
    expect(enrollment.state).toBe(EnrollmentState.WAITLISTED);

    const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    const courseResponse = await apiClient.getCourse(term.id, course.id, regHeaders);
    const updatedCourse = getSuccessData(courseResponse);
    expect(updatedCourse.available_seats).toBe(0);
    expect(updatedCourse.waitlist_count).toBeGreaterThan(0);
  });
});

describe('TC-55: Enroll in Course - Registrar Override Credit Limit', () => {
  it('should allow registrar to enroll student beyond credit limit', async () => {
    const { term: studentTerm } = await createStudentMaxCreditLoad(TEST_USERS.STUDENT_A.id);

    const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
    const courseResponse = await apiClient.createCourse(studentTerm.id, {
      code: 'CS999',
      title: 'Extra Course',
      credits: 3,
      capacity: 5,
      professor_id: TEST_USERS.PROFESSOR_A.id,
      delivery_mode: DeliveryMode.IN_PERSON,
      location: 'Room 999'
    }, profHeaders);
    const draftCourse = getSuccessData(courseResponse);

    const publishResponse = await apiClient.publishCourse(studentTerm.id, draftCourse.id, { revision: draftCourse.revision }, profHeaders);
    const course = getSuccessData(publishResponse);

    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const response = await apiClient.createEnrollment(
      studentTerm.id,
      course.id,
      { student_id: TEST_USERS.STUDENT_A.id },
      registrarHeaders
    );

    const enrollment = handleApiResponse(response, 201);
    expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
    expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_A.id);
  });
});

describe('TC-56: Enroll in Course - Credit Limit Exceeded (Student)', () => {
  it('should block student from exceeding 18-credit term limit', async () => {
    const { term: studentTerm } = await createStudentMaxCreditLoad(TEST_USERS.STUDENT_A.id);

    const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
    const courseResponse = await apiClient.createCourse(studentTerm.id, {
      code: 'CS999',
      title: 'Extra Course',
      credits: 3,
      capacity: 3,
      professor_id: TEST_USERS.PROFESSOR_A.id,
      delivery_mode: DeliveryMode.ONLINE,
      online_link: 'https://example.com/cs999'
    }, profHeaders);
    const draftCourse = getSuccessData(courseResponse);

    const publishResponse = await apiClient.publishCourse(studentTerm.id, draftCourse.id, { revision: draftCourse.revision }, profHeaders);
    const course = getSuccessData(publishResponse);

    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(studentTerm.id, course.id, {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_CREDIT_LIMIT_EXCEEDED });
  });
});

describe('TC-57: Enroll in Course - Unauthorized Role', () => {
  it('should reject professor attempt to use enrollment endpoint', async () => {
    const { term, course } = await createCourseOpen();
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);

    const response = await apiClient.createEnrollment(
      term.id,
      course.id,
      { student_id: TEST_USERS.STUDENT_A.id },
      professorHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_UNAUTHORIZED_ROLE });
  });
});

describe('TC-58: Enroll in Course - Student ID Conflict', () => {
  it('should reject student attempting to enroll another student', async () => {
    const { term, course } = await createCourseOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(
      term.id,
      course.id,
      { student_id: TEST_USERS.STUDENT_B.id },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
  });
});

describe('TC-59: Enroll in Course - Registrar Missing Student ID', () => {
  it('should require student_id when registrar enrolls', async () => {
    const { term, course } = await createCourseOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const response = await apiClient.createEnrollment(term.id, course.id, {}, registrarHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD });
  });
});

describe('TC-60: Enroll in Course - Student Not Found', () => {
  it('should reject enrollment for non-existent student', async () => {
    const { term, course } = await createCourseOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const response = await apiClient.createEnrollment(
      term.id,
      course.id,
      { student_id: uuidv4() },
      registrarHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_STUDENT_NOT_FOUND });
  });
});

describe('TC-61: Enroll in Course - Course Not Found', () => {
  it('should reject enrollment for invalid course ID', async () => {
    const { term } = await createTermEnrollmentOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, uuidv4(), {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_COURSE_NOT_FOUND });
  });
});

describe('TC-62: Enroll in Course - Course Not Open', () => {
  it('should reject enrollment in draft course', async () => {
    const { term } = await createTermEnrollmentOpen();
    const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const createResponse = await apiClient.createCourse(
      term.id,
      {
        code: 'DRF001',
        title: 'Draft Course',
        credits: 3,
        capacity: 10,
        professor_id: TEST_USERS.PROFESSOR_A.id,
        delivery_mode: DeliveryMode.HYBRID,
        location: 'Room 001',
        online_link: 'https://example.com/drf001'
      },
      regHeaders
    );
    const draftCourse = getSuccessData(createResponse);

    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, draftCourse.id, {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_COURSE_WRONG_STATE });
  });
});

describe('TC-63: Enroll in Course - Registration Closed', () => {
  it('should reject enrollment when term registration is closed', async () => {
    const { term: openTerm, course } = await createCourseOpen();

    const regHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
    await apiClient.closeTermRegistration(openTerm.id, { revision: openTerm.revision }, regHeaders);

    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(openTerm.id, course.id, {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REGISTRATION_CLOSED });
  });
});

describe('TC-64: Enroll in Course - Already Enrolled', () => {
  it('should reject duplicate enrollment attempt', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ALREADY_ENROLLED });
  });
});

describe('TC-65: Enroll in Course - Term in Planning State', () => {
  it('should reject enrollment when term is in PLANNING state', async () => {
    const { term, course } = await createCourseDraft();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

    // Accept either error code as both are valid for planning state
    const validErrorIds = [ApiErrorId.ERR_REGISTRATION_CLOSED, ApiErrorId.ERR_TERM_NOT_ACTIVE];
    expect([400, 409]).toContain(response.status);
    expect(validErrorIds).toContain((response.data.data as any).error_id);
  });
});

describe('TC-66: Enroll in Course - Invalid UUID Format', () => {
  it('should reject enrollment with malformed termId', async () => {
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment('invalid-uuid', uuidv4(), {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
  });

  it('should reject enrollment with malformed courseId', async () => {
    const { term } = await createTermEnrollmentOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, 'invalid-uuid', {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
  });

  it('should reject registrar enrollment with malformed student_id', async () => {
    const { term, course } = await createCourseOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const response = await apiClient.createEnrollment(
      term.id,
      course.id,
      { student_id: 'invalid-uuid' },
      registrarHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
  });
});

describe('TC-67: Registrar Override - Enroll When Registration Closed', () => {
  it('should allow registrar to enroll student when registration is closed', async () => {
    const { term, course } = await createCourseOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const closeResponse = await apiClient.closeTermRegistration(
      term.id,
      { revision: term.revision },
      registrarHeaders
    );
    const closedTerm = getSuccessData(closeResponse);

    const response = await apiClient.createEnrollment(
      term.id,
      course.id,
      { student_id: TEST_USERS.STUDENT_A.id },
      registrarHeaders
    );

    const enrollment = handleApiResponse(response, 201);
    assertEnrollment(enrollment);
    expect(enrollment.student_id).toBe(TEST_USERS.STUDENT_A.id);
    expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
  });
});

describe('TC-68: Enroll in Course - Term Concluded', () => {
  it('should reject enrollment when term is concluded with ERR_TERM_NOT_ACTIVE', async () => {
    const { term, course } = await createCourseOpen();
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    const closeResponse = await apiClient.closeTermRegistration(
      term.id,
      { revision: term.revision },
      registrarHeaders
    );
    const closedTerm = getSuccessData(closeResponse);

    await apiClient.concludeTerm(
      closedTerm.id,
      { revision: closedTerm.revision },
      registrarHeaders
    );

    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE });
  });
});

describe('TC-69: Drop Enrollment - Student Drops Enrolled Course', () => {
  it('should successfully drop enrolled course and refund tuition', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      studentHeaders
    );

    const droppedEnrollment = handleApiResponse(response, 200);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

    const courseResponse = await apiClient.getCourse(term.id, course.id, studentHeaders);
    const updatedCourse = getSuccessData(courseResponse);
    expect(updatedCourse.available_seats).toBe(course.capacity);
  });
});

describe('TC-70: Drop Enrollment - Student Drops Waitlisted Course', () => {
  it('should successfully remove student from waitlist', async () => {
    const { term, course, enrollments } = await createCourseWithWaitlist();
    const waitlistedEnrollment = enrollments.studentC;
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      waitlistedEnrollment.id,
      { revision: waitlistedEnrollment.revision },
      studentHeaders
    );

    const droppedEnrollment = handleApiResponse(response, 200);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

    const courseResponse = await apiClient.getCourse(term.id, course.id, studentHeaders);
    const updatedCourse = getSuccessData(courseResponse);
    expect(updatedCourse.available_seats).toBe(0);
  });
});

describe('TC-71: Drop Enrollment - Professor Drops Student from Course', () => {
  it('should allow instructor to drop student without penalty', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const profHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      profHeaders
    );

    const droppedEnrollment = handleApiResponse(response, 200);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

  });
});

describe('TC-72: Drop Enrollment - Unauthorized Actor', () => {
  it('should reject unauthorized user attempting to drop enrollment', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);

    const unauthorizedHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      unauthorizedHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_FORBIDDEN });
  });
});

describe('TC-73: Drop Enrollment - Already Dropped/Completed', () => {
  it('should reject dropping already completed enrollment', async () => {
    const { term, course, enrollments } = await createCourseCompleted();
    const completedEnrollment = enrollments[0];
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      completedEnrollment.id,
      { revision: completedEnrollment.revision },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE });
  });

  it('should reject dropping already dropped enrollment', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const dropResponse = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      studentHeaders
    );
    const droppedEnrollment = getSuccessData(dropResponse);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      droppedEnrollment.id,
      { revision: droppedEnrollment.revision },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_ENROLLMENT_WRONG_STATE });
  });
});

describe('TC-75: Drop Enrollment - Term Concluded (No Student Drop)', () => {
  it('should reject student drop after term concluded', async () => {
    const { term: concludedTerm } = await createTermConcluded();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const fakeEnrollmentId = uuidv4();
    const fakeCourseId = uuidv4();

    const response = await apiClient.dropEnrollment(
      concludedTerm.id,
      fakeCourseId,
      fakeEnrollmentId,
      { revision: 0 },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TERM_NOT_ACTIVE });
  });
});

describe('TC-76: Drop Enrollment - Drop Limit Exceeded', () => {
  it('should block student from dropping 4th course', async () => {
    const { term, droppedCourses, nextCourse } = await createStudentWithDroppedCourses(3, TEST_USERS.STUDENT_A.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const enrollment = await getStudentEnrollment(term.id, nextCourse.id, TEST_USERS.STUDENT_A.id);
    expect(enrollment).toBeTruthy();

    const response = await apiClient.dropEnrollment(
      term.id,
      nextCourse.id,
      enrollment!.id,
      { revision: enrollment!.revision },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_TOO_MANY_DROPS });
  });
});

describe('TC-77: Drop Enrollment - Drop Penalty Fee on 3rd Drop', () => {
  it('should apply $50 penalty fee on 3rd drop', async () => {
    const { term, droppedCourses, nextCourse } = await createStudentWithDroppedCourses(2, TEST_USERS.STUDENT_A.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const enrollment = await getStudentEnrollment(term.id, nextCourse.id, TEST_USERS.STUDENT_A.id);
    expect(enrollment).toBeTruthy();


    const response = await apiClient.dropEnrollment(
      term.id,
      nextCourse.id,
      enrollment!.id,
      { revision: enrollment!.revision },
      studentHeaders
    );

    const droppedEnrollment = handleApiResponse(response, 200);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);


    const paymentResp = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_A.id,
      { amount: DROP_PENALTY_FEE },
      studentHeaders
    );

    const paymentResult = handleApiResponse(paymentResp, 200);
    expect(paymentResult.new_balance).toBe(0);
  });

  it('should not apply penalty on drops 1 and 2', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_B.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);

    const dropResponse = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      studentHeaders
    );

    const droppedEnrollment = handleApiResponse(dropResponse, 200);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

    const paymentResponse = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_B.id,
      { amount: 1 },
      studentHeaders
    );

    validateErrorEnvelope(paymentResponse, { expectedErrorId: ApiErrorId.ERR_OVERPAY_NOT_ALLOWED });
  });
});

describe('TC-79: Drop Enrollment - Revision Conflict', () => {
  it('should reject drop with stale revision', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision - 1 },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REV_CONFLICT });
  });
});

describe('TC-80: Drop Enrollment - Invalid UUID Format', () => {
  it('should reject drop with malformed termId', async () => {
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      'invalid-uuid',
      uuidv4(),
      uuidv4(),
      { revision: 0 },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
  });

  it('should reject drop with malformed courseId', async () => {
    const { term } = await createTermEnrollmentOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      term.id,
      'invalid-uuid',
      uuidv4(),
      { revision: 0 },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
  });

  it('should reject drop with malformed enrollmentId', async () => {
    const { term, course } = await createCourseOpen();
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      'invalid-uuid',
      { revision: 0 },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_INVALID_ID_FORMAT });
  });
});

describe('TC-83: Drop Enrollment - After Registration Closes', () => {
  it('should block student from dropping after registration closes', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    await apiClient.closeTermRegistration(term.id, { revision: term.revision }, registrarHeaders);

    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_REGISTRATION_CLOSED });
  });

  it('should allow registrar to drop after registration closes', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    await apiClient.closeTermRegistration(term.id, { revision: term.revision }, registrarHeaders);

    const response = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      registrarHeaders
    );

    const droppedEnrollment = handleApiResponse(response, 200);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);
  });
});

describe('TC-85: Professor Drop - No Student Drop Count Impact', () => {
  it('should not count professor-initiated drops toward student drop limit', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
    const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

    // Professor drops the student
    const dropResponse = await apiClient.dropEnrollment(
      term.id,
      course.id,
      enrollment.id,
      { revision: enrollment.revision },
      professorHeaders
    );

    const droppedEnrollment = handleApiResponse(dropResponse, 200);
    expect(droppedEnrollment.state).toBe(EnrollmentState.DROPPED);

    // Create 4 additional courses for the student to drop themselves
    // Since professor drop doesn't count, student needs 4 drops to test the limit
    // 3rd student drop should trigger penalty, 4th should be blocked
    const courses = [];
    for (let i = 0; i < 4; i++) {
      const courseResp = await apiClient.createCourse(
        term.id,
        {
          code: `TC${100 + i}`,
          title: `Test Course ${i + 1}`,
          credits: 3,
          capacity: 10,
          professor_id: TEST_USERS.PROFESSOR_B.id,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${200 + i}`
        },
        registrarHeaders
      );
      const newCourse = getSuccessData(courseResp);

      const profBHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);
      await apiClient.publishCourse(term.id, newCourse.id, { revision: newCourse.revision }, profBHeaders);

      // Enroll student
      const enrollResp = await apiClient.createEnrollment(term.id, newCourse.id, {}, studentHeaders);
      const newEnrollment = getSuccessData(enrollResp);

      courses.push({ course: newCourse, enrollment: newEnrollment });
    }

    // Pay for all courses first to clear the balance
    const totalCourseCost = 4 * 3 * 10000; // 4 courses * 3 credits * $100 per credit
    const payAllResp = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_A.id,
      { amount: totalCourseCost },
      studentHeaders
    );
    const payAllResult = handleApiResponse(payAllResp, 200);
    expect(payAllResult.new_balance).toBe(0);

    // Student drops first 3 courses - since professor drop doesn't count, 3rd drop gets penalty
    for (let i = 0; i < 3; i++) {
      const { course: courseToDelete, enrollment: enrollmentToDelete } = courses[i];

      const studentDropResp = await apiClient.dropEnrollment(
        term.id,
        courseToDelete.id,
        enrollmentToDelete.id,
        { revision: enrollmentToDelete.revision },
        studentHeaders
      );

      const studentDroppedEnrollment = handleApiResponse(studentDropResp, 200);
      expect(studentDroppedEnrollment.state).toBe(EnrollmentState.DROPPED);
    }

    // Verify penalty was applied by attempting to pay exactly the penalty amount
    // If professor drops don't count, then 3 student drops should trigger penalty
    // The balance should be: -(3 courses * 3 credits * $100) + $50 penalty = -$850
    // So we need to pay $850 to get to $0
    const expectedRefunds = 3 * 3 * 10000; // 90000 cents refund from 3 courses
    const netAmountToPay = expectedRefunds - DROP_PENALTY_FEE; // 90000 - 5000 = 85000

    const paymentResp = await apiClient.makePayment(
      term.id,
      TEST_USERS.STUDENT_A.id,
      { amount: netAmountToPay },
      studentHeaders
    );

    const paymentResult = handleApiResponse(paymentResp, 200);
    expect(paymentResult.new_balance).toBe(0);

    // Attempt 4th drop - should be blocked since limit is reached
    const { course: fourthCourse, enrollment: fourthEnrollment } = courses[3];
    const fourthDropResp = await apiClient.dropEnrollment(
      term.id,
      fourthCourse.id,
      fourthEnrollment.id,
      { revision: fourthEnrollment.revision },
      studentHeaders
    );

    validateErrorEnvelope(fourthDropResp, { expectedErrorId: ApiErrorId.ERR_TOO_MANY_DROPS });
  });
});

describe('TC-86: Drop Enrollment - Missing Revision Field', () => {
  it('should reject drop when revision field is not provided', async () => {
    const { term, course, enrollment } = await createStudentEnrolledCourse(TEST_USERS.STUDENT_A.id);
    const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

    const response = await makeRequest(
      'PATCH',
      `/terms/${term.id}/courses/${course.id}/enrollments/${enrollment.id}:drop`,
      {},
      studentHeaders
    );

    validateErrorEnvelope(response, { expectedErrorId: ApiErrorId.ERR_MISSING_REQUIRED_FIELD });
  });
});