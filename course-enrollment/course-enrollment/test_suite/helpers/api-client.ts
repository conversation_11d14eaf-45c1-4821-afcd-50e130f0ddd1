/**
 * @fileoverview API client for testing.
 *
 * This file provides utilities for making HTTP requests to your API.
 * Add your domain-specific endpoint methods to the apiClient object.
 */

import axios, { AxiosResponse, Method } from 'axios';
import {
  ApiResponse,
} from './types';

/**
 * Base URL for the API under test.
 * Override by setting the environment variable `API_URL`, e.g.:
 *   API_URL="http://localhost:4000" npm test
 */
export const BASE_URL = process.env.API_URL ?? 'http://localhost:3000';

/**
 * Generic helper for performing HTTP requests in tests.
 *
 * @template TRes  Expected successful data type (`data` inside the envelope).
 * @template TReq  Request body or query-param type (defaults to `unknown`).
 * @param method   HTTP method, e.g. `'GET'`, `'POST'`.
 * @param path     Endpoint path beginning with `/`, e.g. `/v1/resources`.
 * @param paramsOrData Optional query params (`GET`/`DELETE`) or request body
 *                     (`POST`/`PUT`/`PATCH`).
 * @param headers  Optional extra request headers (Idempotency-Key,
 *                 Authorization, etc.).
 * @returns {Promise<AxiosResponse<ApiResponse<TRes>>>}
 */
export async function makeRequest<
  TRes,
  TReq = unknown
>(
  method: Method,
  path: string,
  paramsOrData?: TReq | Record<string, unknown>,
  headers: Record<string, string> = {}
): Promise<AxiosResponse<ApiResponse<TRes>>> {
  const config: Record<string, unknown> = {
    method,
    url: `${BASE_URL}${path}`,
    headers,
    validateStatus: () => true, // Resolve for *all* HTTP status codes
  };

  // Distinguish between query parameters vs. request body based on method
  if (paramsOrData !== undefined) {
    if (['GET', 'get', 'DELETE', 'delete'].includes(method as string)) {
      (config as any).params = paramsOrData;
    } else {
      (config as any).data = paramsOrData;
    }
  }

  return axios(config);
}

// Helper interfaces for request headers
export interface ApiHeadersBase {
  'X-User-ID': string;
  'X-User-Role': string;
  [key: string]: string; // Index signature to allow compatibility with Record<string, string>
}

/**
 * Exported API client object for Course Enrollment API.
 */
export const apiClient = {
  // Term Management
  createTerm: (data: { name: string }, headers: ApiHeadersBase) =>
    makeRequest<AcademicTerm>('POST', '/terms', data, headers),
    
  getTerm: (termId: string, headers: ApiHeadersBase) =>
    makeRequest<AcademicTerm>('GET', `/terms/${termId}`, undefined, headers),
    
  openTermRegistration: (termId: string, data: { revision: number }, headers: ApiHeadersBase) =>
    makeRequest<AcademicTerm>('PATCH', `/terms/${termId}:open-registration`, data, headers),
    
  closeTermRegistration: (termId: string, data: { revision: number }, headers: ApiHeadersBase) =>
    makeRequest<AcademicTerm>('PATCH', `/terms/${termId}:close-registration`, data, headers),
    
  concludeTerm: (termId: string, data: { revision: number }, headers: ApiHeadersBase) =>
    makeRequest<AcademicTerm>('PATCH', `/terms/${termId}:conclude`, data, headers),
    
  // Course Management
  createCourse: (termId: string, data: CreateCoursePayload, headers: ApiHeadersBase) =>
    makeRequest<Course>('POST', `/terms/${termId}/courses`, data, headers),
    
  getCourse: (termId: string, courseId: string, headers: ApiHeadersBase) =>
    makeRequest<Course>('GET', `/terms/${termId}/courses/${courseId}`, undefined, headers),
    
  listCourses: (termId: string, params: { state?: string; professor_id?: string; limit?: number; offset?: number }, headers: ApiHeadersBase) =>
    makeRequest<Course[]>('GET', `/terms/${termId}/courses`, params, headers),
    
  publishCourse: (termId: string, courseId: string, data: { revision: number }, headers: ApiHeadersBase) =>
    makeRequest<Course>('PATCH', `/terms/${termId}/courses/${courseId}:publish`, data, headers),
    
  cancelCourse: (termId: string, courseId: string, data: { revision: number }, headers: ApiHeadersBase) =>
    makeRequest<Course>('PATCH', `/terms/${termId}/courses/${courseId}:cancel`, data, headers),
    
  // Enrollment Management
  createEnrollment: (termId: string, courseId: string, data: { student_id?: string }, headers: ApiHeadersBase) =>
    makeRequest<Enrollment>('POST', `/terms/${termId}/courses/${courseId}/enrollments`, data, headers),
    
  getEnrollment: (termId: string, courseId: string, enrollmentId: string, headers: ApiHeadersBase) =>
    makeRequest<Enrollment>('GET', `/terms/${termId}/courses/${courseId}/enrollments/${enrollmentId}`, undefined, headers),
    
  listEnrollments: (termId: string, courseId: string, params: { limit?: number; offset?: number }, headers: ApiHeadersBase) =>
    makeRequest<Enrollment[]>('GET', `/terms/${termId}/courses/${courseId}/enrollments`, params, headers),
    
  dropEnrollment: (termId: string, courseId: string, enrollmentId: string, data: { revision: number }, headers: ApiHeadersBase) =>
    makeRequest<Enrollment>('PATCH', `/terms/${termId}/courses/${courseId}/enrollments/${enrollmentId}:drop`, data, headers),
    
  // Payment Management
  makePayment: (termId: string, studentId: string, data: { amount: number }, headers: ApiHeadersBase) =>
    makeRequest<{ student_id: string; term_id: string; new_balance: number }>('POST', `/terms/${termId}/students/${studentId}:pay`, data, headers),

  getBalance: (termId: string, studentId: string, headers: ApiHeadersBase) =>
    makeRequest<{ student_id: string; term_id: string; balance_cents: number }>('GET', `/terms/${termId}/students/${studentId}/balance`, undefined, headers),
} as const;

// Import types
import {
  AcademicTerm,
  Course,
  Enrollment,
  DeliveryMode
} from './types';

// Type definitions for request payloads
export interface CreateCoursePayload {
  code: string;
  title: string;
  description?: string;
  credits: number;
  capacity: number;
  professor_id?: string;
  delivery_mode: DeliveryMode;
  location?: string;
  online_link?: string;
}