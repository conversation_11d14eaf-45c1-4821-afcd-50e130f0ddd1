import {
  apiClient,
  createHeaders,
  TEST_USERS,
  createTermEnrollmentOpen,
  createTermPlanning, getSuccessData,
  UserRole,
  ApiErrorId,
  DeliveryMode,
  CreateCoursePayload,
  EnrollmentState,
  CourseState
} from './helpers';

describe('High-Value Functional Tests', () => {
  describe('T-TERM-01: Double-open of term', () => {
    it('should reject opening an already open term with ERR_TERM_NOT_ACTIVE', async () => {
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      const response = await apiClient.openTermRegistration(
        term.id,
        { revision: term.revision },
        registrarHeaders
      );

      expect(response.status).toBe(409);
      const error = response.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_TERM_NOT_ACTIVE);
    });
  });

  describe('T-TERM-02: Concurrent operations with revision conflict', () => {
    it('should reject concurrent open-registration with stale revision', async () => {
      const { term } = await createTermPlanning();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      const response1 = await apiClient.openTermRegistration(
        term.id,
        { revision: term.revision },
        registrarHeaders
      );
      expect(response1.status).toBe(200);

      const response2 = await apiClient.openTermRegistration(
        term.id,
        { revision: term.revision },
        registrarHeaders
      );

      expect(response2.status).toBe(409);
      const error = response2.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_REV_CONFLICT);
    });
  });

  describe('T-TERM-02B: State validation precedence on close-registration', () => {
    it('should check state before revision when closing registration', async () => {
      const { term } = await createTermPlanning();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      const response = await apiClient.closeTermRegistration(
        term.id,
        { revision: term.revision + 1 },
        registrarHeaders
      );

      expect(response.status).toBe(409);
      const error = response.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_TERM_NOT_ACTIVE);
    });
  });

  describe('T-TERM-03: Cascade behavior on close-registration', () => {
    it('should transition all OPEN courses to IN_PROGRESS when closing registration', async () => {
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const courseCodes = ['CAS101', 'CAS102', 'CAS103'];
      const courseIds: string[] = [];

      for (const code of courseCodes) {
        const courseData: CreateCoursePayload = {
          code,
          title: `Cascade Test ${code}`,
          credits: 3,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${code}`
        };

        const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(createResp);

        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          professorHeaders
        );

        courseIds.push(course.id);
      }

      const termResp = await apiClient.getTerm(term.id, registrarHeaders);
      const termData = getSuccessData(termResp);
      await apiClient.closeTermRegistration(
        term.id,
        { revision: termData.revision },
        registrarHeaders
      );

      for (const courseId of courseIds) {
        const courseResp = await apiClient.getCourse(term.id, courseId, professorHeaders);
        const course = getSuccessData(courseResp);
        expect(course.state).toBe(CourseState.IN_PROGRESS);
      }
    });
  });

  describe('T-CRS-01: Professor course limit', () => {
    it('should reject 6th course creation with ERR_MAX_COURSES_REACHED', async () => {
      const { term } = await createTermEnrollmentOpen();
      const uniqueProfessorId = '66666666-7777-8888-9999-000000000000';
      const professorHeaders = createHeaders(uniqueProfessorId, UserRole.PROFESSOR);

      for (let i = 0; i < 5; i++) {
        const courseData: CreateCoursePayload = {
          code: `MAX${i + 1}01`,
          title: `Max Course Test ${i + 1}`,
          credits: 3,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${i + 1}00`
        };

        const resp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        expect(resp.status).toBe(201);
      }

      const courseData: CreateCoursePayload = {
        code: 'MAX601',
        title: 'Max Course Test 6',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/max601'
      };

      const resp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      expect(resp.status).toBe(409);
      const error = resp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_MAX_COURSES_REACHED);
    });
  });

  describe('T-CRS-04: Ledger initialization on publish', () => {
    it('should initialize seat ledger with seats_available == capacity on publish', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const courseData: CreateCoursePayload = {
        code: 'LED101',
        title: 'Ledger Test',
        credits: 3,
        capacity: 25,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Lecture Hall A'
      };

      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const draftCourse = getSuccessData(createResp);

      expect(draftCourse.available_seats).toBe(0);

      const publishResp = await apiClient.publishCourse(
        term.id,
        draftCourse.id,
        { revision: draftCourse.revision },
        professorHeaders
      );
      const publishedCourse = getSuccessData(publishResp);

      expect(publishedCourse.available_seats).toBe(25);
      expect(publishedCourse.state).toBe(CourseState.OPEN);
    });
  });

  describe('T-ENR-01/02: Credit limit vs registrar override', () => {
    it('should block student self-enrollment over 18 credits but allow registrar override', async () => {
      const { term } = await createTermEnrollmentOpen();
      const uniqueStudentId = 'aaaaaaaa-bbbb-cccc-dddd-eeeeeeeeeeee';
      const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const coursesData = [
        { code: 'CRD501', credits: 5 },
        { code: 'CRD502', credits: 5 },
        { code: 'CRD401', credits: 4 },
        { code: 'CRD301', credits: 3 }
      ];

      for (const courseInfo of coursesData) {
        const courseData: CreateCoursePayload = {
          code: courseInfo.code,
          title: `Credit Test ${courseInfo.code}`,
          credits: courseInfo.credits,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room ${courseInfo.code}`
        };

        const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(createResp);

        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          professorHeaders
        );

        const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
        expect(enrollResp.status).toBe(201);
      }

      const overloadCourse: CreateCoursePayload = {
        code: 'CRD402',
        title: 'Credit Overload Test',
        credits: 4,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/crd402'
      };

      const createResp = await apiClient.createCourse(term.id, overloadCourse, professorHeaders);
      const course = getSuccessData(createResp);

      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );

      const studentEnrollResp = await apiClient.createEnrollment(
        term.id,
        course.id,
        {},
        studentHeaders
      );
      expect(studentEnrollResp.status).toBe(409);
      const error = studentEnrollResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_CREDIT_LIMIT_EXCEEDED);

      const registrarEnrollResp = await apiClient.createEnrollment(
        term.id,
        course.id,
        { student_id: uniqueStudentId },
        registrarHeaders
      );
      expect(registrarEnrollResp.status).toBe(201);
      const enrollment = getSuccessData(registrarEnrollResp);
      expect(enrollment.state).toBe(EnrollmentState.ENROLLED);
    });
  });

  describe('T-ENR-04: Waitlist promotion FIFO mechanics', () => {
    it('should promote oldest waitlisted student when seat becomes available', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const enrolledStudent = '11111111-2222-3333-4444-555555555555';
      const waitlistStudent1 = '22222222-3333-4444-5555-666666666666';
      const waitlistStudent2 = '33333333-4444-5555-6666-777777777777';

      const enrolledHeaders = createHeaders(enrolledStudent, UserRole.STUDENT);
      const waitlist1Headers = createHeaders(waitlistStudent1, UserRole.STUDENT);
      const waitlist2Headers = createHeaders(waitlistStudent2, UserRole.STUDENT);

      const courseData: CreateCoursePayload = {
        code: 'WLP101',
        title: 'Waitlist Promotion Test',
        credits: 3,
        capacity: 1,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Small Room'
      };

      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(createResp);

      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );

      const enroll1Resp = await apiClient.createEnrollment(term.id, course.id, {}, enrolledHeaders);
      const enrollment1 = getSuccessData(enroll1Resp);
      expect(enrollment1.state).toBe(EnrollmentState.ENROLLED);

      const enroll2Resp = await apiClient.createEnrollment(term.id, course.id, {}, waitlist1Headers);
      const enrollment2 = getSuccessData(enroll2Resp);
      expect(enrollment2.state).toBe(EnrollmentState.WAITLISTED);

      await new Promise(resolve => setTimeout(resolve, 10));

      const enroll3Resp = await apiClient.createEnrollment(term.id, course.id, {}, waitlist2Headers);
      const enrollment3 = getSuccessData(enroll3Resp);
      expect(enrollment3.state).toBe(EnrollmentState.WAITLISTED);

      await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment1.id,
        { revision: enrollment1.revision },
        enrolledHeaders
      );

      const updated2Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment2.id,
        waitlist1Headers
      );
      const updated2 = getSuccessData(updated2Resp);
      expect(updated2.state).toBe(EnrollmentState.ENROLLED);

      const updated3Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment3.id,
        waitlist2Headers
      );
      const updated3 = getSuccessData(updated3Resp);
      expect(updated3.state).toBe(EnrollmentState.WAITLISTED);

      const courseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
      const updatedCourse = getSuccessData(courseResp);
      expect(updatedCourse.available_seats).toBe(0);
    });
  });

  describe('T-DRP-01: Third drop penalty fee', () => {
    it('should apply $50 penalty on 3rd self-initiated drop', async () => {
      const { term } = await createTermEnrollmentOpen();
      const uniqueStudentId = '44444444-5555-6666-7777-888888888888';
      const studentHeaders = createHeaders(uniqueStudentId, UserRole.STUDENT);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);

      const courses = [];
      for (let i = 0; i < 3; i++) {
        const courseData: CreateCoursePayload = {
          code: `PEN${i + 1}01`,
          title: `Penalty Test ${i + 1}`,
          credits: 1,
          capacity: 10,
          delivery_mode: DeliveryMode.IN_PERSON,
          location: `Room P${i + 1}`
        };

        const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
        const course = getSuccessData(createResp);

        await apiClient.publishCourse(
          term.id,
          course.id,
          { revision: course.revision },
          professorHeaders
        );

        courses.push(course);
      }

      for (let i = 0; i < 3; i++) {
        const enrollResp = await apiClient.createEnrollment(
          term.id,
          courses[i].id,
          {},
          studentHeaders
        );
        const enrollment = getSuccessData(enrollResp);

        const dropResp = await apiClient.dropEnrollment(
          term.id,
          courses[i].id,
          enrollment.id,
          { revision: enrollment.revision },
          studentHeaders
        );

        expect(dropResp.status).toBe(200);

        if (i === 2) {
          const payResp = await apiClient.makePayment(
            term.id,
            uniqueStudentId,
            { amount: 5000 },
            studentHeaders
          );
          expect(payResp.status).toBe(200);

          const overpayResp = await apiClient.makePayment(
            term.id,
            uniqueStudentId,
            { amount: 1 },
            studentHeaders
          );
          expect(overpayResp.status).toBe(422);
          const error = overpayResp.data as any;
          expect(error.data.error_id).toBe(ApiErrorId.ERR_OVERPAY_NOT_ALLOWED);
        }
      }
    });
  });

  describe('T-DRP-03: Professor drop permission after enrollment closed', () => {
    it('should reject professor drop when term is ENROLLMENT_CLOSED', async () => {
      const { term } = await createTermEnrollmentOpen();
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      const courseData: CreateCoursePayload = {
        code: 'DRP301',
        title: 'Drop Permission Test',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room D301'
      };

      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(createResp);

      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );

      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);

      const termResp = await apiClient.getTerm(term.id, registrarHeaders);
      const termData = getSuccessData(termResp);
      await apiClient.closeTermRegistration(
        term.id,
        { revision: termData.revision },
        registrarHeaders
      );

      const dropResp = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        professorHeaders
      );

      expect(dropResp.status).toBe(409);
      const error = dropResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_REGISTRATION_CLOSED);
    });
  });

  describe('T-CANC-01: Course cancellation cascade effects', () => {
    it('should drop all enrollments and refund enrolled students on cancel', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const enrolledStudent1 = '11111111-2222-3333-4444-444444444444';
      const enrolledStudent2 = '22222222-3333-4444-5555-555555555555';
      const waitlistedStudent = '33333333-4444-5555-6666-666666666666';

      const enrolled1Headers = createHeaders(enrolledStudent1, UserRole.STUDENT);
      const enrolled2Headers = createHeaders(enrolledStudent2, UserRole.STUDENT);
      const waitlistedHeaders = createHeaders(waitlistedStudent, UserRole.STUDENT);

      const courseData: CreateCoursePayload = {
        code: 'CAN101',
        title: 'Cancellation Test',
        credits: 4,
        capacity: 2,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/can101'
      };

      const createResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(createResp);

      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorHeaders
      );

      const courseResp = await apiClient.getCourse(term.id, course.id, professorHeaders);
      const publishedCourse = getSuccessData(courseResp);

      const enroll1Resp = await apiClient.createEnrollment(term.id, course.id, {}, enrolled1Headers);
      const enrollment1 = getSuccessData(enroll1Resp);
      expect(enrollment1.state).toBe(EnrollmentState.ENROLLED);

      const enroll2Resp = await apiClient.createEnrollment(term.id, course.id, {}, enrolled2Headers);
      const enrollment2 = getSuccessData(enroll2Resp);
      expect(enrollment2.state).toBe(EnrollmentState.ENROLLED);

      const enroll3Resp = await apiClient.createEnrollment(term.id, course.id, {}, waitlistedHeaders);
      const enrollment3 = getSuccessData(enroll3Resp);
      expect(enrollment3.state).toBe(EnrollmentState.WAITLISTED);

      await apiClient.makePayment(term.id, enrolledStudent1, { amount: 20000 }, enrolled1Headers);
      await apiClient.makePayment(term.id, enrolledStudent2, { amount: 30000 }, enrolled2Headers);

      await apiClient.cancelCourse(
        term.id,
        course.id,
        { revision: publishedCourse.revision },
        professorHeaders
      );

      const updated1Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment1.id,
        enrolled1Headers
      );
      const updated1 = getSuccessData(updated1Resp);
      expect(updated1.state).toBe(EnrollmentState.DROPPED);

      const updated2Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment2.id,
        enrolled2Headers
      );
      const updated2 = getSuccessData(updated2Resp);
      expect(updated2.state).toBe(EnrollmentState.DROPPED);

      const updated3Resp = await apiClient.getEnrollment(
        term.id,
        course.id,
        enrollment3.id,
        waitlistedHeaders
      );
      const updated3 = getSuccessData(updated3Resp);
      expect(updated3.state).toBe(EnrollmentState.DROPPED);

      const pay1Resp = await apiClient.makePayment(
        term.id,
        enrolledStudent1,
        { amount: 1 },
        enrolled1Headers
      );
      expect(pay1Resp.status).toBe(200);
      const pay1Data = getSuccessData(pay1Resp);
      expect(pay1Data.new_balance).toBe(-19999);

      const pay2Resp = await apiClient.makePayment(
        term.id,
        enrolledStudent2,
        { amount: 1 },
        enrolled2Headers
      );
      expect(pay2Resp.status).toBe(200);
      const pay2Data = getSuccessData(pay2Resp);
      expect(pay2Data.new_balance).toBe(-29999);

      const pay3Resp = await apiClient.makePayment(
        term.id,
        waitlistedStudent,
        { amount: 1 },
        waitlistedHeaders
      );
      expect(pay3Resp.status).toBe(422);
    });
  });

  describe('T-PAY-01: Zero payment validation', () => {
    it('should reject payment with amount 0', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);

      const payResp = await apiClient.makePayment(
        term.id,
        TEST_USERS.STUDENT_A.id,
        { amount: 0 },
        studentHeaders
      );

      expect(payResp.status).toBe(422);
      const error = payResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_INVALID_PAYMENT_AMOUNT);
    });
  });

  describe('T-VIS-01/02/03: Field visibility by role', () => {
    it('should enforce role-based field visibility rules', async () => {
      const { term } = await createTermEnrollmentOpen();
      const professorAHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const professorBHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const registrarHeaders = createHeaders(TEST_USERS.REGISTRAR.id, UserRole.REGISTRAR);

      const courseData: CreateCoursePayload = {
        code: 'VIS101',
        title: 'Visibility Test',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room V101'
      };

      const createResp = await apiClient.createCourse(term.id, courseData, professorAHeaders);
      const course = getSuccessData(createResp);

      await apiClient.publishCourse(
        term.id,
        course.id,
        { revision: course.revision },
        professorAHeaders
      );

      await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);

      const studentListResp = await apiClient.listCourses(term.id, {}, studentHeaders);
      const studentCourses = getSuccessData(studentListResp);
      const studentCourseView = studentCourses.find((c: any) => c.id === course.id);
      expect(studentCourseView).toBeDefined();
      expect(studentCourseView!.enrolled_count).toBeUndefined();
      expect(studentCourseView!.waitlist_count).toBeUndefined();

      const profCourseResp = await apiClient.getCourse(term.id, course.id, professorAHeaders);
      const profCourseView = getSuccessData(profCourseResp) as any;
      expect(profCourseView.enrollments).toBeDefined();
      expect(Array.isArray(profCourseView.enrollments)).toBe(true);
      expect(profCourseView.enrollments.length).toBe(1);
      expect(profCourseView.enrolled_count).toBe(1);

      const draftCourseData: CreateCoursePayload = {
        code: 'VIS201',
        title: 'Draft Visibility Test',
        credits: 3,
        capacity: 5,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/vis201'
      };

      const draftResp = await apiClient.createCourse(term.id, draftCourseData, professorBHeaders);
      const draftCourse = getSuccessData(draftResp);

      const otherProfResp = await apiClient.getCourse(term.id, draftCourse.id, professorAHeaders);
      expect(otherProfResp.status).toBe(404);
      const error = otherProfResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_COURSE_NOT_FOUND);

      const registrarCourseResp = await apiClient.getCourse(term.id, course.id, registrarHeaders);
      const registrarCourseView = getSuccessData(registrarCourseResp) as any;
      expect(registrarCourseView.enrollments).toBeDefined();
      expect(registrarCourseView.enrolled_count).toBe(1);
      expect(registrarCourseView.waitlist_count).toBe(0);
    });
  });
  describe('CG-01: Waitlist promotion credit limit overflow', () => {
    it('should block waitlist promotion when it would exceed credit limit', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const course1Data: CreateCoursePayload = {
        code: 'CG101',
        title: 'Coverage Gap Course 1',
        credits: 5,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/cg101'
      };
      const course1Resp = await apiClient.createCourse(term.id, course1Data, professorHeaders);
      const course1 = getSuccessData(course1Resp);
      await apiClient.publishCourse(term.id, course1.id, { revision: course1.revision }, professorHeaders);

      const course1bData: CreateCoursePayload = {
        code: 'CG102',
        title: 'Coverage Gap Course 2',
        credits: 5,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room 101'
      };
      const course1bResp = await apiClient.createCourse(term.id, course1bData, professorHeaders);
      const course1b = getSuccessData(course1bResp);
      await apiClient.publishCourse(term.id, course1b.id, { revision: course1b.revision }, professorHeaders);

      const course1cData: CreateCoursePayload = {
        code: 'CG103',
        title: 'Coverage Gap Course 3',
        credits: 5,
        capacity: 10,
        delivery_mode: DeliveryMode.HYBRID,
        location: 'Room 102',
        online_link: 'https://example.com/cg103'
      };
      const course1cResp = await apiClient.createCourse(term.id, course1cData, professorHeaders);
      const course1c = getSuccessData(course1cResp);
      await apiClient.publishCourse(term.id, course1c.id, { revision: course1c.revision }, professorHeaders);

      const course1dData: CreateCoursePayload = {
        code: 'CG104',
        title: 'Coverage Gap Course 4',
        credits: 2,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/cg104'
      };
      const course1dResp = await apiClient.createCourse(term.id, course1dData, professorHeaders);
      const course1d = getSuccessData(course1dResp);
      await apiClient.publishCourse(term.id, course1d.id, { revision: course1d.revision }, professorHeaders);

      await apiClient.createEnrollment(term.id, course1.id, {}, studentHeaders);
      await apiClient.createEnrollment(term.id, course1b.id, {}, studentHeaders);
      await apiClient.createEnrollment(term.id, course1c.id, {}, studentHeaders);
      await apiClient.createEnrollment(term.id, course1d.id, {}, studentHeaders);

      const course2Data: CreateCoursePayload = {
        code: 'CG105',
        title: 'Coverage Gap Course 5',
        credits: 2,
        capacity: 1,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room 105'
      };
      const course2Resp = await apiClient.createCourse(term.id, course2Data, professorHeaders);
      const course2 = getSuccessData(course2Resp);
      await apiClient.publishCourse(term.id, course2.id, { revision: course2.revision }, professorHeaders);

      const otherStudentHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
      await apiClient.createEnrollment(term.id, course2.id, {}, otherStudentHeaders);

      const enrollResp = await apiClient.createEnrollment(term.id, course2.id, {}, studentHeaders);
      expect(enrollResp.status).toBe(409);
      const error = enrollResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_CREDIT_LIMIT_EXCEEDED);
    });
  });

  describe('CG-02: Unknown field rejection', () => {
    it('should reject unknown fields in enrollment creation', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);

      const courseData: CreateCoursePayload = {
        code: 'CG201',
        title: 'Coverage Gap Course for Unknown Fields',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/cg201'
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

      const enrollmentData = {
        student_id: TEST_USERS.STUDENT_C.id,
        unknown_field: 'should_be_rejected'
      };

      const response = await apiClient.createEnrollment(term.id, course.id, enrollmentData, studentHeaders);
      expect(response.status).toBe(400);
      const error = response.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_UNKNOWN_FIELD);
    });
  });

  describe('CG-03: Professor authorization for student drops', () => {
    it('should allow professor to drop their own course students', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_A.id, UserRole.STUDENT);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const courseData: CreateCoursePayload = {
        code: 'CG301',
        title: 'Coverage Gap Course for Professor Drop',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.IN_PERSON,
        location: 'Room 301'
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);

      const dropResp = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        professorHeaders
      );
      expect(dropResp.status).toBe(200);
    });

    it('should block professor from dropping students from other professors courses', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_B.id, UserRole.STUDENT);
      const professor1Headers = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);
      const professor2Headers = createHeaders(TEST_USERS.PROFESSOR_B.id, UserRole.PROFESSOR);

      const courseData: CreateCoursePayload = {
        code: 'CG302',
        title: 'Coverage Gap Course for Auth Test',
        credits: 3,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/cg302'
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professor1Headers);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professor1Headers);

      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);

      const dropResp = await apiClient.dropEnrollment(
        term.id,
        course.id,
        enrollment.id,
        { revision: enrollment.revision },
        professor2Headers
      );
      expect(dropResp.status).toBe(403);
      const error = dropResp.data as any;
      expect(error.data.error_id).toBe(ApiErrorId.ERR_NOT_INSTRUCTOR);
    });
  });

  describe('CG-04: Negative balance scenarios', () => {
    it('should allow negative balance after full refund with partial payment', async () => {
      const { term } = await createTermEnrollmentOpen();
      const studentHeaders = createHeaders(TEST_USERS.STUDENT_C.id, UserRole.STUDENT);
      const professorHeaders = createHeaders(TEST_USERS.PROFESSOR_A.id, UserRole.PROFESSOR);

      const courseData: CreateCoursePayload = {
        code: 'CG401',
        title: 'Coverage Gap Course for Negative Balance',
        credits: 5,
        capacity: 10,
        delivery_mode: DeliveryMode.ONLINE,
        online_link: 'https://example.com/cg401'
      };
      const courseResp = await apiClient.createCourse(term.id, courseData, professorHeaders);
      const course = getSuccessData(courseResp);
      await apiClient.publishCourse(term.id, course.id, { revision: course.revision }, professorHeaders);

      const enrollResp = await apiClient.createEnrollment(term.id, course.id, {}, studentHeaders);
      const enrollment = getSuccessData(enrollResp);

      await apiClient.makePayment(term.id, TEST_USERS.STUDENT_C.id, { amount: 45000 }, studentHeaders);

      await apiClient.dropEnrollment(term.id, course.id, enrollment.id, { revision: enrollment.revision }, studentHeaders);

      const paymentResp = await apiClient.makePayment(term.id, TEST_USERS.STUDENT_C.id, { amount: 10000 }, studentHeaders);
      expect(paymentResp.status).toBe(200);
      const paymentData = getSuccessData(paymentResp);

      expect(paymentData.new_balance).toBe(-35000);
    });
  });
});